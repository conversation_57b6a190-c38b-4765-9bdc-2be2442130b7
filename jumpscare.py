import cv2
import pygame
import time
import threading
import keyboard  # To block key input
import os

# === SETTINGS ===
video_path = "scare.mp4"   # Put your horror video here
scary_text = "He is watching you... Don't turn around."
delay_seconds = 5          # Wait time before video starts

# === SCARY TEXT THREAD ===
def type_scare_text():
    pygame.font.init()
    font = pygame.font.SysFont("Courier", 40)
    typed = ""
    for char in scary_text:
        typed += char
        text_surface = font.render(typed, True, (255, 0, 0))
        screen.blit(text_surface, (screen_w//2 - len(typed)*10, screen_h - 100))
        pygame.display.update()
        time.sleep(0.1)

# === LOCK KEYBOARD (no escape homie) ===
keyboard.block_key('esc')
keyboard.block_key('alt')
keyboard.block_key('tab')
keyboard.block_key('ctrl')
keyboard.block_key('delete')
keyboard.block_key('f4')
keyboard.block_key('win')

# === WAIT BEFORE STARTING ===
time.sleep(delay_seconds)

# === INIT PYGAME FULLSCREEN ===
pygame.init()
screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
pygame.mouse.set_visible(False)
screen_w = pygame.display.Info().current_w
screen_h = pygame.display.Info().current_h

# === PLAY VIDEO + TEXT ===
cap = cv2.VideoCapture(video_path)
threading.Thread(target=type_scare_text, daemon=True).start()

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break
    frame = cv2.resize(frame, (screen_w, screen_h))
    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    surface = pygame.surfarray.make_surface(frame.swapaxes(0, 1))
    screen.blit(surface, (0, 0))
    pygame.display.update()

cap.release()

# === UNLOCK KEYS AFTER JUMPSCARE ===
keyboard.unhook_all()
pygame.quit()
